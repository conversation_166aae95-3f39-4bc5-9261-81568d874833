import hmac
import hashlib
import json

SECRET_KEY = b'supersecretkey'

def generate_hmac_sha512(body: dict) -> str:
    body_str = json.dumps(body, separators=(',', ':'), sort_keys=True)
    return hmac.new(SECRET_KEY, body_str.encode('utf-8'), hashlib.sha512).hexdigest()

def verify_hmac(request_body: dict, client_hash: str) -> bool:
    expected_hash = generate_hmac_sha512(request_body)
    return hmac.compare_digest(expected_hash, client_hash)
