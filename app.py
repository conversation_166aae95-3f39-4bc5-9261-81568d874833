from flask import Flask, request, jsonify
import os
import requests
from PIL import Image
from io import BytesIO
import uuid

from utils import verify_hmac

app = Flask(__name__)
UPLOAD_FOLDER = 'static/images'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/convert-webp', methods=['POST'])
def convert_webp():
    client_hash = request.headers.get('Authorization')
    if not client_hash:
        return jsonify({'status': False, 'message': 'Header Authorization (HMAC) tidak ditemukan'}), 401

    try:
        body_json = request.get_json(force=True)
    except Exception:
        return jsonify({'status': False, 'message': 'Body JSON tidak valid'}), 400

    if not verify_hmac(body_json, client_hash):
        return jsonify({'status': False, 'message': 'Hash HMAC tidak valid'}), 403

    url = body_json.get('url_gambar')
    persentase = body_json.get('persentase_kompresi', 60)

    if not url:
        return jsonify({'status': False, 'message': 'url_gambar tidak boleh kosong'}), 400

    try:
        response = requests.get(url)
        if response.status_code != 200:
            raise Exception("Gagal mengambil gambar dari URL")
        image = Image.open(BytesIO(response.content))
    except Exception as e:
        return jsonify({'status': False, 'message': f'Error mengambil gambar: {str(e)}'}), 400

    try:
        filename = f"{uuid.uuid4().hex}.webp"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        image.convert("RGB").save(filepath, "webp", quality=int(persentase))

        ukuran_kb = os.path.getsize(filepath) // 1024
        url_webp = f"/{filepath}"

        return jsonify({
            'status': True,
            'message': 'Konversi berhasil',
            'url_webp': url_webp,
            'ukuran_webp': f'{ukuran_kb} KB'
        })

    except Exception as e:
        return jsonify({'status': False, 'message': f'Error konversi gambar: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True)

