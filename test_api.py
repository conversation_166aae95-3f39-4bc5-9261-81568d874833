import requests
import json
import hmac
import hashlib

# Ganti sesuai dengan secret key yang kamu pakai di utils.py
SECRET_KEY = b'supersecretkey'

data = {
    "url_gambar": "https://upload.wikimedia.org/wikipedia/commons/thumb/4/47/PNG_transparency_demonstration_1.png/800px-PNG_transparency_demonstration_1.png",
    "persentase_kompresi": 60
}

# Buat hash HMAC SHA-512 dari body JSON
body_str = json.dumps(data, separators=(',', ':'), sort_keys=True)
signature = hmac.new(SECRET_KEY, body_str.encode('utf-8'), hashlib.sha512).hexdigest()

# Kirim request ke server lokal
response = requests.post(
    "http://127.0.0.1:5000/convert-webp",
    json=data,
    headers={
        "Content-Type": "application/json",
        "Authorization": signature
    }
)

print("Status Code:", response.status_code)
print("Response JSON:", response.json())
